package com.ebon.energy.fms.domain.po;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.Pattern;
import javax.validation.constraints.Size;
import java.util.List;

/**
 * 创建推送任务请求对象
 */
@Data
public class CreatePushTaskPO {

    /**
     * 目标操作系统: ALL / IOS / ANDROID
     */
    @NotBlank(message = "操作系统不能为空")
    @Pattern(regexp = "^(ALL|IOS|ANDROID)$", message = "操作系统必须是 ALL、IOS 或 ANDROID")
    private String os;

    /**
     * 客户端版本号列表，允许 "ALL"
     */
    @NotEmpty(message = "版本列表不能为空")
    private List<String> versionList;

    /**
     * 推送标题
     */
    @NotBlank(message = "推送标题不能为空")
    @Size(max = 255, message = "推送标题长度不能超过50个字符")
    private String title;

    /**
     * 推送内容正文
     */
    @NotBlank(message = "推送内容不能为空")
//    @Size(max = 1000, message = "推送内容长度不能超过1000个字符")
    private String content;

    /**
     * 计划发送时间（Unix 毫秒时间戳）；空或 0 表示立即发送
     */
    private Long pushTime;
}
