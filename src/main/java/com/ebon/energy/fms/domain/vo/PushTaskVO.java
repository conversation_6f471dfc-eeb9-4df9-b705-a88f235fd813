package com.ebon.energy.fms.domain.vo;

import lombok.Data;

import java.util.List;

/**
 * 推送任务响应对象
 */
@Data
public class PushTaskVO {

    /**
     * 主键ID
     */
    private Long id;

    /**
     * 目标操作系统: ALL / IOS / ANDROID
     */
    private String os;

    /**
     * 客户端版本号列表
     */
    private List<String> versionList;

    /**
     * 推送标题
     */
    private String title;

    /**
     * 推送内容正文
     */
    private String content;

    /**
     * 计划发送时间（Unix 毫秒时间戳）
     */
    private Long pushTime;

    /**
     * 任务状态: PENDING / SENT / FAILED
     */
    private String status;

    /**
     * 已重试次数
     */
    private Integer retryCount;

    /**
     * 任务创建时间（Unix 毫秒时间戳）
     */
    private Long createdAt;

    /**
     * 最后更新时间（Unix 毫秒时间戳）
     */
    private Long updatedAt;

    /**
     * 创建人用户名 / ID
     */
    private String createdBy;

    /**
     * 最后更新人用户名 / ID
     */
    private String updatedBy;
}
