package com.ebon.energy.fms.domain.po;

import lombok.Data;

import javax.validation.constraints.Min;

/**
 * 推送任务查询请求对象
 */
@Data
public class PushTaskQueryPO {

    /**
     * 当前页码
     */
    @Min(value = 1, message = "页码必须大于0")
    private Integer current = 1;

    /**
     * 每页大小
     */
    @Min(value = 1, message = "每页大小必须大于0")
    private Integer pageSize = 20;

    /**
     * 操作系统过滤: ALL / IOS / ANDROID
     */
    private String os;

    /**
     * 状态过滤: PENDING / SENT / FAILED
     */
    private String status;

    /**
     * 关键词搜索（标题或内容）
     */
    private String keyword;
}
